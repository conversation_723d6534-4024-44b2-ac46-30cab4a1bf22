import { z } from 'zod';
import { basePerTenantAndOrganizationEntitySchema, taskSchema } from './task.schema';

/**
 * Daily Plan Status Enum Schema
 */
export const dailyPlanStatusSchema = z.enum(['open', 'in-progress', 'completed']);

/**
 * Base Daily Plan Schema
 * Represents the core properties of a daily plan
 */
export const dailyPlanBaseSchema = basePerTenantAndOrganizationEntitySchema.extend({
	date: z.coerce.date(),
	workTimePlanned: z.string(),
	status: dailyPlanStatusSchema
});

/**
 * Daily Plan Schema
 * Represents a complete daily plan with all relations
 */
export const dailyPlanSchema = dailyPlanBaseSchema.extend({
	// Employee relation
	employeeId: z.string().optional().nullable(),
	employee: z.any().optional().nullable(), // Employee schema would be complex, using any for now

	// Organization Team relation
	organizationTeamId: z.string().optional().nullable(),
	organizationTeam: z.any().optional().nullable(), // OrganizationTeam schema, using any for now

	// Tasks relation
	tasks: z.array(taskSchema).optional().nullable()
});

/**
 * Create Daily Plan Schema
 * For creating new daily plans
 */
export const createDailyPlanSchema = dailyPlanBaseSchema.omit({ id: true, createdAt: true, updatedAt: true }).extend({
	employeeId: z.string(),
	organizationTeamId: z.string().optional().nullable(),
	taskId: z.string().optional().nullable()
});

/**
 * Update Daily Plan Schema
 * For updating existing daily plans
 */
export const updateDailyPlanSchema = dailyPlanBaseSchema.partial().extend({
	employeeId: z.string(),
	organizationTeamId: z.string().optional().nullable()
});

/**
 * Daily Plan Tasks Update Schema
 * For adding/removing tasks from daily plans
 */
export const dailyPlanTasksUpdateSchema = z.object({
	taskId: z.string().optional().nullable(),
	employeeId: z.string(),
	tenantId: z.string().optional().nullable(),
	organizationId: z.string().optional().nullable()
});

/**
 * Remove Task From Many Plans Request Schema
 */
export const removeTaskFromManyPlansRequestSchema = z.object({
	employeeId: z.string().optional().nullable(),
	plansIds: z.array(z.string()).optional().nullable(),
	organizationId: z.string().optional().nullable()
});

// Export TypeScript types
export type TDailyPlanStatus = z.infer<typeof dailyPlanStatusSchema>;
export type TDailyPlanBase = z.infer<typeof dailyPlanBaseSchema>;
export type TDailyPlan = z.infer<typeof dailyPlanSchema>;
export type TCreateDailyPlan = z.infer<typeof createDailyPlanSchema>;
export type TUpdateDailyPlan = z.infer<typeof updateDailyPlanSchema>;
export type TDailyPlanTasksUpdate = z.infer<typeof dailyPlanTasksUpdateSchema>;
export type TRemoveTaskFromManyPlansRequest = z.infer<typeof removeTaskFromManyPlansRequestSchema>;
